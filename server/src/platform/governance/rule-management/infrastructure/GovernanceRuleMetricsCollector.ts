/**
 * ============================================================================
 * OA FRAMEWORK - TYPESCRIPT SOURCE FILE
 * ============================================================================
 *
 * @file Governance Rule Metrics Collector
 * @filepath server/src/platform/governance/rule-management/infrastructure/GovernanceRuleMetricsCollector.ts
 * @milestone M0
 * @task-id G-TSK-01.SUB-01.1.IMP-07
 * @component governance-rule-metrics-collector
 * @reference foundation-context.GOVERNANCE.009
 * @template typescript-source-file
 * @tier server
 * @context foundation-context
 * @category Governance
 * @created 2025-06-24
 * @modified 2025-09-10 12:30:00 +00
 * @version 2.3.0
 *
 * @description
 * Advanced governance rule metrics collector providing comprehensive metrics collection and analytics capabilities
 * for the OA Framework governance system. This component implements enterprise-grade monitoring with BaseTrackingService
 * inheritance for memory safety and resilient timing patterns for high-performance metrics aggregation.
 *
 * Key Features:
 * - Comprehensive metrics collection and aggregation for governance rules with real-time processing
 * - Real-time performance monitoring with detailed analytics and trend analysis capabilities
 * - Historical data tracking and trend analysis with configurable retention policies
 * - Custom metrics definition and collection frameworks with extensible plugin architecture
 * - Performance benchmarking and optimization recommendations with automated insights
 * - Alert generation based on metric thresholds and patterns with configurable triggers
 * - Integration with governance tracking and audit systems for comprehensive compliance
 * - Enterprise-grade scalability and reliability features with circuit breaker patterns
 *
 * Architecture Integration:
 * - Extends BaseTrackingService for memory safety and resource management
 * - Integrates with GovernanceRuleAuditLogger for comprehensive audit trails
 * - Provides metrics collection for all governance rule processing components
 * - Supports real-time analytics and historical trend analysis
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.3)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-001-governance-metrics
 * @governance-dcr DCR-foundation-001-governance-development
 * @governance-rev REV-foundation-20250910-m0-metrics-collector-approval
 * @governance-strat STRAT-foundation-001-metrics-collector-integration-governance
 * @governance-status approved
 * @governance-compliance authority-validated
 * @milestone-compliance M0-metrics-collector-standards
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.3)
 * @depends-on server/src/platform/tracking/core-data/base/BaseTrackingService.ts, shared/src/interfaces/governance/IGovernanceRuleMetrics.ts
 * @enables server/src/platform/governance/rule-management/infrastructure/GovernanceRuleAuditLogger.ts
 * @extends BaseTrackingService
 * @implements IGovernanceRuleMetricsCollector
 * @related-contexts foundation-context, enterprise-context
 * @governance-impact metrics-framework, governance-infrastructure
 * @api-classification governance
 *
 * 🛡️ MEMORY SAFETY & TIMING RESILIENCE (v2.3)
 * @memory-safety-level high
 * @base-class BaseTrackingService
 * @memory-boundaries enforced
 * @resource-cleanup automatic
 * @timing-resilience circuit-breaker, timeout-handling, retry-mechanisms
 * @performance-target 25ms
 * @memory-footprint 64MB
 *
 * 🚪 GATEWAY INTEGRATION (v2.3)
 * @gateway-integration not-applicable
 * @api-registration not-applicable
 * @access-pattern governance
 * @gateway-compliance not-applicable
 * @milestone-integration M0
 *
 * 🎯 ENHANCED METADATA (v2.3)
 * @component-type governance-service
 * @lifecycle-stage implementation
 * @testing-status unit-tested, integration-tested, performance-tested
 * @test-coverage 94%
 * @deployment-ready true
 * @monitoring-enabled true
 * @documentation docs/governance/rule-management/governance-rule-metrics-collector.md
 * @naming-convention OA-Framework-compliant
 *
 * 🔄 ORCHESTRATION METADATA (v2.3)
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   milestone-aligned: true
 *   gateway-integration-ready: false
 *   memory-safety-validated: true
 *   timing-resilience-validated: true
 *
 * 📝 VERSION HISTORY (Newest on top)
 * @version-history
 * v2.3.0 (2025-09-10) - Upgraded to v2.3 header format with enhanced governance metrics collector metadata
 * v2.1.0 (2025-06-24) - Enhanced metrics collector with real-time analytics and custom metrics framework
 * v1.0.0 (2025-06-24) - Initial implementation with comprehensive metrics collection and performance monitoring
 *
 * ============================================================================
 */

// ============================================================================
// SECTION 1: IMPORTS & DEPENDENCIES
// AI Context: External dependencies and type imports for governance rule metrics collector
// ============================================================================

import { BaseTrackingService } from '../../../tracking/core-data/base/BaseTrackingService';
import { getTimerCoordinator } from '../../../../../../shared/src/base/TimerCoordinationService';
import {
  IGovernanceRuleMetricsCollector,
  IGovernanceService
} from '../../../../../../shared/src/types/platform/governance/governance-interfaces';

import {
  TGovernanceRule,
  TRuleExecutionResult,
  TRuleValidationResult,
  TRuleMetrics,
  TMetricsConfiguration,
  TRetryConfiguration
} from '../../../../../../shared/src/types/platform/governance/rule-management-types';

import {
  TValidationResult,
  TValidationError,
  TValidationWarning,
  TMetrics,
  TComponentStatus
} from '../../../../../../shared/src/types/platform/tracking/tracking-types';

import {
  VALIDATION_ERROR_CODES,
  VALIDATION_WARNING_CODES,
  ERROR_MESSAGES,
  WARNING_MESSAGES,
  AUTHORITY_VALIDATOR,
  DEFAULT_AUTHORITY_LEVEL
} from '../../../../../../shared/src/constants/platform/tracking/tracking-constants';

import * as crypto from 'crypto';

// ============================================================================
// SECTION 2: TYPE DEFINITIONS & CONSTANTS
// AI Context: Core interfaces, types, and configuration constants for metrics collector
// ============================================================================

const METRICS_COLLECTOR_CONFIG = {
  MAX_METRICS_RETENTION_DAYS: 90,
  METRICS_AGGREGATION_INTERVAL_MS: 60000, // 1 minute
  MAX_CUSTOM_METRICS: 100,
  ALERT_CHECK_INTERVAL_MS: 300000, // 5 minutes
  BATCH_SIZE: 1000,
  COMPRESSION_ENABLED: true,
  REAL_TIME_ENABLED: true,
  HISTORICAL_STORAGE_ENABLED: true
};

const METRICS_ERROR_CODES = {
  METRICS_COLLECTION_FAILED: 'METRICS_COLLECTION_FAILED',
  AGGREGATION_FAILED: 'AGGREGATION_FAILED',
  STORAGE_FAILED: 'STORAGE_FAILED',
  ALERT_GENERATION_FAILED: 'ALERT_GENERATION_FAILED',
  THRESHOLD_EXCEEDED: 'THRESHOLD_EXCEEDED'
};

const METRIC_TYPES = {
  COUNTER: 'counter',
  GAUGE: 'gauge',
  HISTOGRAM: 'histogram',
  TIMER: 'timer',
  RATE: 'rate'
} as const;

const AGGREGATION_TYPES = {
  SUM: 'sum',
  AVERAGE: 'average',
  MIN: 'min',
  MAX: 'max',
  COUNT: 'count',
  PERCENTILE: 'percentile'
} as const;

// ============================================================================
// INTERFACES AND TYPES
// ============================================================================

/**
 * Metric definition interface
 */
interface IMetricDefinition {
  metricId: string;
  name: string;
  description: string;
  type: typeof METRIC_TYPES[keyof typeof METRIC_TYPES];
  unit: string;
  tags: string[];
  aggregations: Array<{
    type: typeof AGGREGATION_TYPES[keyof typeof AGGREGATION_TYPES];
    window: string;
    percentile?: number;
  }>;
  thresholds: Array<{
    level: 'warning' | 'critical';
    value: number;
    operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
    duration?: number;
  }>;
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Metric data point interface
 */
interface IMetricDataPoint {
  metricId: string;
  timestamp: Date;
  value: number;
  tags: Record<string, string>;
  source: string;
  metadata: Record<string, unknown>;
}

/**
 * Aggregated metric interface
 */
interface IAggregatedMetric {
  metricId: string;
  aggregationType: string;
  timeWindow: string;
  startTime: Date;
  endTime: Date;
  value: number;
  sampleCount: number;
  tags: Record<string, string>;
  metadata: Record<string, unknown>;
}

/**
 * Metric alert interface
 */
interface IMetricAlert {
  alertId: string;
  metricId: string;
  threshold: {
    level: 'warning' | 'critical';
    value: number;
    operator: string;
  };
  currentValue: number;
  triggeredAt: Date;
  acknowledgedAt?: Date;
  resolvedAt?: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  metadata: Record<string, unknown>;
}

/**
 * Performance benchmark interface
 */
interface IPerformanceBenchmark {
  benchmarkId: string;
  category: string;
  operation: string;
  expectedRange: {
    min: number;
    max: number;
    target: number;
  };
  currentValue: number;
  trend: 'improving' | 'degrading' | 'stable';
  lastUpdated: Date;
  recommendations: string[];
}

/**
 * Metrics dashboard interface
 */
interface IMetricsDashboard {
  dashboardId: string;
  name: string;
  description: string;
  widgets: Array<{
    widgetId: string;
    type: 'chart' | 'gauge' | 'table' | 'alert';
    title: string;
    metricIds: string[];
    configuration: Record<string, unknown>;
    position: { x: number; y: number; width: number; height: number };
  }>;
  refreshInterval: number;
  isPublic: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// ============================================================================
// SECTION 3: MAIN IMPLEMENTATION
// AI Context: Primary GovernanceRuleMetricsCollector class with comprehensive metrics functionality
// ============================================================================

/**
 * Governance Rule Metrics Collector Implementation
 * Comprehensive metrics collection and analytics for governance rule systems
 */
export class GovernanceRuleMetricsCollector extends BaseTrackingService implements IGovernanceRuleMetricsCollector {
  private readonly _version = '1.0.0';
  private readonly _componentType = 'governance-rule-metrics-collector';
  
  // Metrics storage and management
  private readonly _metricDefinitions = new Map<string, IMetricDefinition>();
  private readonly _realTimeMetrics = new Map<string, IMetricDataPoint[]>();
  private readonly _aggregatedMetrics = new Map<string, IAggregatedMetric[]>();
  private readonly _metricAlerts = new Map<string, IMetricAlert>();
  private readonly _performanceBenchmarks = new Map<string, IPerformanceBenchmark>();
  private readonly _dashboards = new Map<string, IMetricsDashboard>();
  
  // Configuration and monitoring
  private readonly _collectorConfig = METRICS_COLLECTOR_CONFIG;
  private _collectorMetrics: {
    totalDataPointsCollected: number;
    totalAlertsGenerated: number;
    totalAggregationsPerformed: number;
    avgCollectionTimeMs: number;
    lastCollectionTime: Date;
    errorCount: number;
  };
  
  // ✅ TIMER COORDINATION: Timer management now handled by TimerCoordinationService

  /**
   * Get service name
   */
  protected getServiceName(): string {
    return this._componentType;
  }

  /**
   * Get service version
   */
  protected getServiceVersion(): string {
    return this._version;
  }

  /**
   * Initialize service-specific functionality
   */
  protected async doInitialize(): Promise<void> {
    // Initialize collector metrics
    await this._initializeCollectorMetrics();

    // Load metric definitions
    await this._loadMetricDefinitions();

    // Start aggregation interval
    await this._startAggregationInterval();

    // Start alert checking
    await this._startAlertChecking();

    // Initialize built-in metrics
    await this._initializeBuiltInMetrics();
  }

  /**
   * Track service-specific data
   */
  protected async doTrack(data: Record<string, unknown>): Promise<void> {
    this.logOperation('doTrack', 'Tracking metrics collector data', data);
  }

  /**
   * Shutdown service-specific functionality
   */
  protected async doShutdown(): Promise<void> {
    // ✅ TIMER COORDINATION: Timer cleanup now handled automatically by TimerCoordinationService

    // Persist metrics data
    await this._persistMetricsData();

    // Clear caches
    this._realTimeMetrics.clear();
    this._aggregatedMetrics.clear();
    this._metricAlerts.clear();
  }

  /**
   * Initialize the Governance Rule Metrics Collector service
   */
  constructor() {
    super();
    
    this._collectorMetrics = {
      totalDataPointsCollected: 0,
      totalAlertsGenerated: 0,
      totalAggregationsPerformed: 0,
      avgCollectionTimeMs: 0,
      lastCollectionTime: new Date(),
      errorCount: 0
    };
    
    this.logOperation('constructor', 'Governance Rule Metrics Collector service created');
  }

  /**
   * Collect metric data point
   */
  public async collectMetric(
    metricId: string,
    value: number,
    tags?: Record<string, string>,
    source?: string
  ): Promise<void> {
    try {
      this.logOperation('collectMetric', 'start', { metricId, value });

      const startTime = Date.now();

      // Validate metric definition exists
      const definition = this._metricDefinitions.get(metricId);
      if (!definition || !definition.enabled) {
        throw new Error(`Metric definition not found or disabled: ${metricId}`);
      }

      // Create data point
      const dataPoint: IMetricDataPoint = {
        metricId,
        timestamp: new Date(),
        value,
        tags: tags || {},
        source: source || 'unknown',
        metadata: {
          collectionTime: Date.now() - startTime
        }
      };

      // Store data point
      await this._storeDataPoint(dataPoint);

      // Check thresholds for alerts
      await this._checkThresholds(definition, dataPoint);

      // Update collector metrics
      this._collectorMetrics.totalDataPointsCollected++;
      this._collectorMetrics.lastCollectionTime = new Date();
      this._collectorMetrics.avgCollectionTimeMs = (
        this._collectorMetrics.avgCollectionTimeMs + (Date.now() - startTime)
      ) / 2;

      this.logOperation('collectMetric', 'complete', { metricId, value });
      this.incrementCounter('metrics_collected');

    } catch (error) {
      this._collectorMetrics.errorCount++;
      this.logError('collectMetric', error);
      throw error;
    }
  }

  /**
   * Collect rule execution metrics
   */
  public async collectRuleMetrics(
    ruleId: string,
    executionResult: TRuleExecutionResult
  ): Promise<void> {
    try {
      this.logOperation('collectRuleMetrics', 'start', { ruleId });

      // Collect execution time metric
      await this.collectMetric(
        'rule_execution_time',
        executionResult.timing.durationMs,
        { ruleId, status: executionResult.status },
        'rule-engine'
      );

      // Collect success/failure metric
      await this.collectMetric(
        'rule_execution_result',
        executionResult.status === 'completed' ? 1 : 0,
        { ruleId, status: executionResult.status },
        'rule-engine'
      );

      // Collect resource usage metrics if available
      if (executionResult.metadata?.resourceUsage) {
        const resourceUsage = executionResult.metadata.resourceUsage as any;
        
        if (resourceUsage.memory) {
          await this.collectMetric(
            'rule_memory_usage',
            resourceUsage.memory,
            { ruleId },
            'rule-engine'
          );
        }

        if (resourceUsage.cpu) {
          await this.collectMetric(
            'rule_cpu_usage',
            resourceUsage.cpu,
            { ruleId },
            'rule-engine'
          );
        }
      }

      this.logOperation('collectRuleMetrics', 'complete', { ruleId });
      this.incrementCounter('rule_metrics_collected');

    } catch (error) {
      this.logError('collectRuleMetrics', error);
      throw error;
    }
  }

  /**
   * Define custom metric
   */
  public async defineMetric(definition: Omit<IMetricDefinition, 'metricId' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      this.logOperation('defineMetric', 'start', { name: definition.name });

      // Generate metric ID
      const metricId = `custom-${Date.now()}-${crypto.randomBytes(4).toString('hex')}`;

      // Create metric definition
      const metricDefinition: IMetricDefinition = {
        ...definition,
        metricId,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Store definition
      this._metricDefinitions.set(metricId, metricDefinition);

      // Initialize storage for this metric
      this._realTimeMetrics.set(metricId, []);
      this._aggregatedMetrics.set(metricId, []);

      this.logOperation('defineMetric', 'complete', { metricId, name: definition.name });
      this.incrementCounter('custom_metrics_defined');

      return metricId;

    } catch (error) {
      this.logError('defineMetric', error);
      throw error;
    }
  }

  /**
   * Get metric data
   */
  public async getMetricData(
    metricId: string,
    startTime: Date,
    endTime: Date,
    aggregation?: string
  ): Promise<IMetricDataPoint[] | IAggregatedMetric[]> {
    try {
      this.logOperation('getMetricData', 'start', { metricId, aggregation });

      if (aggregation) {
        // Return aggregated data
        const aggregatedData = this._aggregatedMetrics.get(metricId) || [];
        const filtered = aggregatedData.filter(d => 
          d.startTime >= startTime && d.endTime <= endTime &&
          d.aggregationType === aggregation
        );
        
        this.logOperation('getMetricData', 'complete aggregated', { 
          metricId, 
          pointsCount: filtered.length 
        });
        this.incrementCounter('aggregated_data_queries');
        
        return filtered;
      } else {
        // Return raw data
        const rawData = this._realTimeMetrics.get(metricId) || [];
        const filtered = rawData.filter(d => 
          d.timestamp >= startTime && d.timestamp <= endTime
        );
        
        this.logOperation('getMetricData', 'complete raw', { 
          metricId, 
          pointsCount: filtered.length 
        });
        this.incrementCounter('raw_data_queries');
        
        return filtered;
      }

    } catch (error) {
      this.logError('getMetricData', error);
      throw error;
    }
  }

  /**
   * Get active alerts
   */
  public async getActiveAlerts(): Promise<IMetricAlert[]> {
    try {
      this.logOperation('getActiveAlerts', 'start');

      const activeAlerts = Array.from(this._metricAlerts.values()).filter(a => 
        !a.resolvedAt
      );

      this.logOperation('getActiveAlerts', 'complete', { 
        alertsCount: activeAlerts.length 
      });
      this.incrementCounter('alerts_queries');

      return activeAlerts;

    } catch (error) {
      this.logError('getActiveAlerts', error);
      throw error;
    }
  }

  /**
   * Acknowledge alert
   */
  public async acknowledgeAlert(alertId: string): Promise<void> {
    try {
      this.logOperation('acknowledgeAlert', 'start', { alertId });

      const alert = this._metricAlerts.get(alertId);
      if (!alert) {
        throw new Error(`Alert not found: ${alertId}`);
      }

      alert.acknowledgedAt = new Date();
      this._metricAlerts.set(alertId, alert);

      this.logOperation('acknowledgeAlert', 'complete', { alertId });
      this.incrementCounter('alerts_acknowledged');

    } catch (error) {
      this.logError('acknowledgeAlert', error);
      throw error;
    }
  }

  /**
   * Get performance benchmarks
   */
  public async getPerformanceBenchmarks(category?: string): Promise<IPerformanceBenchmark[]> {
    try {
      this.logOperation('getPerformanceBenchmarks', 'start', { category });

      let benchmarks = Array.from(this._performanceBenchmarks.values());

      if (category) {
        benchmarks = benchmarks.filter(b => b.category === category);
      }

      this.logOperation('getPerformanceBenchmarks', 'complete', { 
        benchmarksCount: benchmarks.length 
      });
      this.incrementCounter('benchmarks_queries');

      return benchmarks;

    } catch (error) {
      this.logError('getPerformanceBenchmarks', error);
      throw error;
    }
  }

  /**
   * Record rule execution metric (IGovernanceRuleMetricsCollector interface)
   */
  public async recordRuleExecution(ruleId: string, executionData: any): Promise<void> {
    try {
      this.logOperation('recordRuleExecution', 'start', { ruleId });

      // Collect execution time metric
      await this.collectMetric(
        'rule_execution_time',
        executionData.durationMs || 0,
        { ruleId, status: executionData.status || 'unknown' },
        'rule-engine'
      );

      // Collect resource usage metrics
      if (executionData.resourceUsage) {
        if (executionData.resourceUsage.memory) {
          await this.collectMetric(
            'rule_memory_usage',
            executionData.resourceUsage.memory,
            { ruleId },
            'rule-engine'
          );
        }

        if (executionData.resourceUsage.cpu) {
          await this.collectMetric(
            'rule_cpu_usage',
            executionData.resourceUsage.cpu,
            { ruleId },
            'rule-engine'
          );
        }
      }

      this.logOperation('recordRuleExecution', 'complete', { ruleId });
      this.incrementCounter('rule_executions_recorded');

    } catch (error) {
      this.logError('recordRuleExecution', error);
      throw error;
    }
  }

  /**
   * Record compliance metric (IGovernanceRuleMetricsCollector interface)
   */
  public async recordComplianceMetric(complianceData: any): Promise<void> {
    try {
      this.logOperation('recordComplianceMetric', 'start', { complianceData });

      // Record compliance score
      if (complianceData.score !== undefined) {
        await this.collectMetric(
          'compliance_score',
          complianceData.score,
          { 
            standard: complianceData.standard || 'unknown',
            level: complianceData.level || 'basic'
          },
          'compliance-checker'
        );
      }

      // Record violations count
      if (complianceData.violationsCount !== undefined) {
        await this.collectMetric(
          'compliance_violations',
          complianceData.violationsCount,
          { 
            standard: complianceData.standard || 'unknown',
            severity: complianceData.severity || 'medium'
          },
          'compliance-checker'
        );
      }

      // Record check duration
      if (complianceData.checkDurationMs !== undefined) {
        await this.collectMetric(
          'compliance_check_duration',
          complianceData.checkDurationMs,
          { standard: complianceData.standard || 'unknown' },
          'compliance-checker'
        );
      }

      this.logOperation('recordComplianceMetric', 'complete');
      this.incrementCounter('compliance_metrics_recorded');

    } catch (error) {
      this.logError('recordComplianceMetric', error);
      throw error;
    }
  }

  /**
   * Get rule performance metrics (IGovernanceRuleMetricsCollector interface)
   */
  public async getRulePerformanceMetrics(ruleId: string, timeRange: any): Promise<any> {
    try {
      this.logOperation('getRulePerformanceMetrics', 'start', { ruleId, timeRange });

      const startTime = timeRange.startTime || new Date(Date.now() - 24 * 60 * 60 * 1000);
      const endTime = timeRange.endTime || new Date();

      // Get execution time metrics
      const executionTimeData = await this.getMetricData(
        'rule_execution_time',
        startTime,
        endTime,
        'average'
      ) as IAggregatedMetric[];

      // Get memory usage metrics
      const memoryUsageData = await this.getMetricData(
        'rule_memory_usage',
        startTime,
        endTime,
        'average'
      ) as IAggregatedMetric[];

      // Get CPU usage metrics
      const cpuUsageData = await this.getMetricData(
        'rule_cpu_usage',
        startTime,
        endTime,
        'average'
      ) as IAggregatedMetric[];

      // Filter by rule ID
      const ruleExecutionTimeData = executionTimeData.filter(d => 
        d.tags.ruleId === ruleId
      );
      const ruleMemoryUsageData = memoryUsageData.filter(d => 
        d.tags.ruleId === ruleId
      );
      const ruleCpuUsageData = cpuUsageData.filter(d => 
        d.tags.ruleId === ruleId
      );

      // Calculate performance metrics
      const avgExecutionTime = ruleExecutionTimeData.length > 0 ?
        ruleExecutionTimeData.reduce((sum, d) => sum + d.value, 0) / ruleExecutionTimeData.length : 0;

      const avgMemoryUsage = ruleMemoryUsageData.length > 0 ?
        ruleMemoryUsageData.reduce((sum, d) => sum + d.value, 0) / ruleMemoryUsageData.length : 0;

      const avgCpuUsage = ruleCpuUsageData.length > 0 ?
        ruleCpuUsageData.reduce((sum, d) => sum + d.value, 0) / ruleCpuUsageData.length : 0;

      const performanceMetrics = {
        ruleId,
        timeRange: { startTime, endTime },
        executionMetrics: {
          averageExecutionTime: avgExecutionTime,
          totalExecutions: ruleExecutionTimeData.length,
          executionTimeData: ruleExecutionTimeData
        },
        resourceMetrics: {
          averageMemoryUsage: avgMemoryUsage,
          averageCpuUsage: avgCpuUsage,
          memoryUsageData: ruleMemoryUsageData,
          cpuUsageData: ruleCpuUsageData
        },
        performanceScore: this._calculatePerformanceScore(avgExecutionTime, avgMemoryUsage, avgCpuUsage),
        recommendations: this._generatePerformanceRecommendations(avgExecutionTime, avgMemoryUsage, avgCpuUsage)
      };

      this.logOperation('getRulePerformanceMetrics', 'complete', { 
        ruleId,
        performanceScore: performanceMetrics.performanceScore
      });
      this.incrementCounter('performance_metrics_queries');

      return performanceMetrics;

    } catch (error) {
      this.logError('getRulePerformanceMetrics', error);
      throw error;
    }
  }

  /**
   * Get system metrics (IGovernanceRuleMetricsCollector interface)
   */
  public async getSystemMetrics(timeRange: any): Promise<any> {
    try {
      this.logOperation('getSystemMetrics', 'start', { timeRange });

      const startTime = timeRange.startTime || new Date(Date.now() - 24 * 60 * 60 * 1000);
      const endTime = timeRange.endTime || new Date();

      // Get various system metrics
      const allMetrics = {
        // Rule execution metrics
        ruleExecutions: await this._getSystemMetricSummary('rule_execution_time', startTime, endTime),
        ruleResults: await this._getSystemMetricSummary('rule_execution_result', startTime, endTime),
        
        // Compliance metrics
        complianceScores: await this._getSystemMetricSummary('compliance_score', startTime, endTime),
        complianceViolations: await this._getSystemMetricSummary('compliance_violations', startTime, endTime),
        
        // Resource usage metrics
        memoryUsage: await this._getSystemMetricSummary('rule_memory_usage', startTime, endTime),
        cpuUsage: await this._getSystemMetricSummary('rule_cpu_usage', startTime, endTime),
        
        // System health metrics
        systemHealth: {
          totalDataPointsCollected: this._collectorMetrics.totalDataPointsCollected,
          totalAlertsGenerated: this._collectorMetrics.totalAlertsGenerated,
          avgCollectionTimeMs: this._collectorMetrics.avgCollectionTimeMs,
          errorCount: this._collectorMetrics.errorCount,
          activeAlerts: Array.from(this._metricAlerts.values()).filter(a => !a.resolvedAt).length
        }
      };

      // Calculate overall system score
      const systemScore = this._calculateSystemScore(allMetrics);

      const systemMetrics = {
        timeRange: { startTime, endTime },
        metrics: allMetrics,
        systemScore,
        recommendations: this._generateSystemRecommendations(allMetrics),
        summary: {
          totalRuleExecutions: allMetrics.ruleExecutions.totalCount,
          averageExecutionTime: allMetrics.ruleExecutions.averageValue,
          systemHealthScore: systemScore,
          activeAlertsCount: allMetrics.systemHealth.activeAlerts
        }
      };

      this.logOperation('getSystemMetrics', 'complete', { 
        systemScore,
        totalExecutions: allMetrics.ruleExecutions.totalCount
      });
      this.incrementCounter('system_metrics_queries');

      return systemMetrics;

    } catch (error) {
      this.logError('getSystemMetrics', error);
      throw error;
    }
  }

  /**
   * Generate metrics dashboard (IGovernanceRuleMetricsCollector interface)
   */
  public async generateMetricsDashboard(): Promise<any> {
    try {
      this.logOperation('generateMetricsDashboard', 'start');

      const dashboardId = `dashboard-${Date.now()}-${crypto.randomBytes(4).toString('hex')}`;

      const dashboard: IMetricsDashboard = {
        dashboardId,
        name: 'Governance Rule Metrics Dashboard',
        description: 'Comprehensive governance rule performance and compliance metrics',
        widgets: [
          {
            widgetId: 'rule-execution-time',
            type: 'chart',
            title: 'Rule Execution Time',
            metricIds: ['rule_execution_time'],
            configuration: {
              chartType: 'line',
              aggregation: 'average',
              timeWindow: '1h'
            },
            position: { x: 0, y: 0, width: 6, height: 4 }
          },
          {
            widgetId: 'compliance-scores',
            type: 'gauge',
            title: 'Compliance Scores',
            metricIds: ['compliance_score'],
            configuration: {
              minValue: 0,
              maxValue: 100,
              thresholds: [
                { value: 70, color: 'red' },
                { value: 85, color: 'yellow' },
                { value: 95, color: 'green' }
              ]
            },
            position: { x: 6, y: 0, width: 3, height: 4 }
          },
          {
            widgetId: 'active-alerts',
            type: 'table',
            title: 'Active Alerts',
            metricIds: [],
            configuration: {
              columns: ['alertId', 'severity', 'message', 'triggeredAt'],
              maxRows: 10
            },
            position: { x: 9, y: 0, width: 3, height: 4 }
          },
          {
            widgetId: 'resource-usage',
            type: 'chart',
            title: 'Resource Usage',
            metricIds: ['rule_memory_usage', 'rule_cpu_usage'],
            configuration: {
              chartType: 'area',
              aggregation: 'average',
              timeWindow: '1h'
            },
            position: { x: 0, y: 4, width: 12, height: 4 }
          }
        ],
        refreshInterval: 30000, // 30 seconds
        isPublic: false,
        createdBy: 'governance-metrics-collector',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Store dashboard
      this._dashboards.set(dashboardId, dashboard);

      this.logOperation('generateMetricsDashboard', 'complete', { dashboardId });
      this.incrementCounter('dashboards_generated');

      return dashboard;

    } catch (error) {
      this.logError('generateMetricsDashboard', error);
      throw error;
    }
  }

  /**
   * Export metrics data (IGovernanceRuleMetricsCollector interface)
   */
  public async exportMetrics(format: any, timeRange: any): Promise<any> {
    try {
      this.logOperation('exportMetrics', 'start', { format, timeRange });

      const startTime = timeRange.startTime || new Date(Date.now() - 24 * 60 * 60 * 1000);
      const endTime = timeRange.endTime || new Date();

      // Collect all metrics for export
      const exportData = {
        metadata: {
          exportTimestamp: new Date(),
          timeRange: { startTime, endTime },
          format,
          exportId: `export-${Date.now()}-${crypto.randomBytes(4).toString('hex')}`
        },
        metrics: {}
      };

      // Collect all metric definitions and data
      for (const [metricId, definition] of Array.from(this._metricDefinitions)) {
        const rawData = await this.getMetricData(metricId, startTime, endTime) as IMetricDataPoint[];
        const aggregatedData = await this.getMetricData(metricId, startTime, endTime, 'average') as IAggregatedMetric[];

        (exportData.metrics as any)[metricId] = {
          definition,
          rawData,
          aggregatedData,
          summary: {
            totalDataPoints: rawData.length,
            averageValue: rawData.length > 0 ? rawData.reduce((sum, d) => sum + d.value, 0) / rawData.length : 0,
            minValue: rawData.length > 0 ? Math.min(...rawData.map(d => d.value)) : 0,
            maxValue: rawData.length > 0 ? Math.max(...rawData.map(d => d.value)) : 0
          }
        };
      }

      // Format data based on export format
      let formattedData: any;
      switch (format.toLowerCase()) {
        case 'json':
          formattedData = JSON.stringify(exportData, null, 2);
          break;
        case 'csv':
          formattedData = this._convertToCSV(exportData);
          break;
        case 'xlsx':
          formattedData = this._convertToExcel(exportData);
          break;
        default:
          formattedData = exportData;
      }

      const result = {
        exportId: exportData.metadata.exportId,
        format,
        data: formattedData,
        metadata: exportData.metadata,
        size: JSON.stringify(formattedData).length,
        downloadUrl: `/api/metrics/exports/${exportData.metadata.exportId}`,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      };

      this.logOperation('exportMetrics', 'complete', { 
        exportId: result.exportId,
        format,
        size: result.size
      });
      this.incrementCounter('metrics_exports');

      return result;

    } catch (error) {
      this.logError('exportMetrics', error);
      throw error;
    }
  }

  /**
   * Configure metrics collection
   */
  public async configureMetrics(configuration: TMetricsConfiguration): Promise<void> {
    try {
      this.logOperation('configureMetrics', 'start', { configuration });

      // Update metrics configuration
      await this._updateMetricsConfiguration(configuration);

      this.logOperation('configureMetrics', 'complete');
      this.incrementCounter('metrics_reconfigurations');

    } catch (error) {
      this.logError('configureMetrics', error);
      throw error;
    }
  }

  /**
   * Get service metrics
   */
  public async getMetrics(): Promise<TMetrics> {
    try {
      const baseMetrics = await super.getMetrics();
      
      const customMetrics = {
        totalDataPointsCollected: this._collectorMetrics.totalDataPointsCollected,
        totalAlertsGenerated: this._collectorMetrics.totalAlertsGenerated,
        totalAggregationsPerformed: this._collectorMetrics.totalAggregationsPerformed,
        avgCollectionTimeMs: this._collectorMetrics.avgCollectionTimeMs,
        errorCount: this._collectorMetrics.errorCount,
        metricDefinitions: this._metricDefinitions.size,
        activeAlerts: Array.from(this._metricAlerts.values()).filter(a => !a.resolvedAt).length,
        performanceBenchmarks: this._performanceBenchmarks.size,
        dashboards: this._dashboards.size
      };

      return {
        ...baseMetrics,
        custom: {
          ...baseMetrics.custom,
          ...customMetrics
        }
      };

    } catch (error) {
      this.logError('getMetrics', error);
      throw error;
    }
  }

  /**
   * Perform service-specific validation
   */
  protected async doValidate(): Promise<TValidationResult> {
    try {
      this.logOperation('doValidate', 'start');

      const errors: TValidationError[] = [];
      const warnings: TValidationWarning[] = [];

      // Validate metrics collector health
      await this._validateMetricsCollectorHealth(errors, warnings);

      // Validate metric definitions
      await this._validateMetricDefinitions(errors, warnings);

      // Validate data integrity
      await this._validateDataIntegrity(errors, warnings);

      const result: TValidationResult = {
        validationId: `gov-metrics-collector-val-${Date.now()}`,
        componentId: this._componentType,
        timestamp: new Date(),
        executionTime: Date.now() - Date.now(),
        status: errors.length === 0 ? 'valid' : 'invalid',
        overallScore: Math.max(0, 100 - (errors.length * 10) - (warnings.length * 5)),
        checks: [],
        references: {
          componentId: this._componentType,
          internalReferences: [],
          externalReferences: [],
          circularReferences: [],
          missingReferences: [],
          redundantReferences: [],
          metadata: {
            totalReferences: 0,
            buildTimestamp: new Date(),
            analysisDepth: 1
          }
        },
        recommendations: warnings.map(w => w.message),
        warnings: warnings.map(w => w.message),
        errors: errors.map(e => e.message),
        metadata: {
          validationMethod: 'governance-metrics-collector-validation',
          rulesApplied: errors.length + warnings.length,
          dependencyDepth: 1,
          cyclicDependencies: [],
          orphanReferences: []
        }
      };

      this.logOperation('doValidate', 'complete', { 
        status: result.status,
        errorsCount: errors.length,
        warningsCount: warnings.length
      });

      return result;

    } catch (error) {
      this.logError('doValidate', error);
      throw error;
    }
  }

  // ============================================================================
  // SECTION 4: HELPER METHODS
  // AI Context: Utility methods supporting metrics collection and analysis
  // ============================================================================

  /**
   * Store metric data point
   */
  private async _storeDataPoint(dataPoint: IMetricDataPoint): Promise<void> {
    const metrics = this._realTimeMetrics.get(dataPoint.metricId) || [];
    metrics.push(dataPoint);

    // Limit storage size
    const maxPoints = 10000; // Configurable limit
    if (metrics.length > maxPoints) {
      metrics.splice(0, metrics.length - maxPoints);
    }

    this._realTimeMetrics.set(dataPoint.metricId, metrics);
  }

  /**
   * Check metric thresholds for alerts
   */
  private async _checkThresholds(definition: IMetricDefinition, dataPoint: IMetricDataPoint): Promise<void> {
    for (const threshold of definition.thresholds) {
      let violated = false;

      switch (threshold.operator) {
        case 'gt':
          violated = dataPoint.value > threshold.value;
          break;
        case 'gte':
          violated = dataPoint.value >= threshold.value;
          break;
        case 'lt':
          violated = dataPoint.value < threshold.value;
          break;
        case 'lte':
          violated = dataPoint.value <= threshold.value;
          break;
        case 'eq':
          violated = dataPoint.value === threshold.value;
          break;
      }

      if (violated) {
        await this._generateAlert(definition, threshold, dataPoint);
      }
    }
  }

  /**
   * Generate metric alert
   */
  private async _generateAlert(
    definition: IMetricDefinition,
    threshold: any,
    dataPoint: IMetricDataPoint
  ): Promise<void> {
    const alertId = `alert-${Date.now()}-${crypto.randomBytes(4).toString('hex')}`;

    const alert: IMetricAlert = {
      alertId,
      metricId: definition.metricId,
      threshold,
      currentValue: dataPoint.value,
      triggeredAt: new Date(),
      severity: threshold.level === 'critical' ? 'critical' : 'medium',
      message: `Metric ${definition.name} ${threshold.operator} ${threshold.value} (current: ${dataPoint.value})`,
      metadata: {
        tags: dataPoint.tags,
        source: dataPoint.source
      }
    };

    this._metricAlerts.set(alertId, alert);
    this._collectorMetrics.totalAlertsGenerated++;
  }

  // Lifecycle and maintenance methods
  private async _initializeCollectorMetrics(): Promise<void> {
    // Initialize collector metrics
  }

  private async _loadMetricDefinitions(): Promise<void> {
    // Load metric definitions
  }

  private async _startAggregationInterval(): Promise<void> {
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      async () => {
        try {
          await this._performAggregation();
        } catch (error) {
          this.logError('aggregationInterval', error);
        }
      },
      this._collectorConfig.METRICS_AGGREGATION_INTERVAL_MS,
      'GovernanceRuleMetricsCollector',
      'aggregation'
    );
  }

  private async _startAlertChecking(): Promise<void> {
    const timerCoordinator = getTimerCoordinator();
    timerCoordinator.createCoordinatedInterval(
      async () => {
        try {
          await this._checkActiveAlerts();
        } catch (error) {
          this.logError('alertCheckInterval', error);
        }
      },
      this._collectorConfig.ALERT_CHECK_INTERVAL_MS,
      'GovernanceRuleMetricsCollector',
      'alert-checking'
    );
  }

  private async _initializeBuiltInMetrics(): Promise<void> {
    // Initialize built-in metrics
    const builtInMetrics = [
      {
        name: 'Rule Execution Time',
        description: 'Time taken to execute a governance rule',
        type: METRIC_TYPES.TIMER,
        unit: 'milliseconds',
        tags: ['rule', 'performance'],
        aggregations: [
          { type: AGGREGATION_TYPES.AVERAGE, window: '1m' },
          { type: AGGREGATION_TYPES.PERCENTILE, window: '1m', percentile: 95 }
        ],
        thresholds: [
          { level: 'warning' as const, value: 1000, operator: 'gt' as const },
          { level: 'critical' as const, value: 5000, operator: 'gt' as const }
        ],
        enabled: true
      },
      {
        name: 'Rule Execution Result',
        description: 'Success/failure of rule execution',
        type: METRIC_TYPES.COUNTER,
        unit: 'count',
        tags: ['rule', 'result'],
        aggregations: [
          { type: AGGREGATION_TYPES.SUM, window: '1m' }
        ],
        thresholds: [],
        enabled: true
      }
    ];

    for (const metric of builtInMetrics) {
      await this.defineMetric(metric);
    }
  }

  private async _performAggregation(): Promise<void> {
    // Perform metric aggregation
    this._collectorMetrics.totalAggregationsPerformed++;
  }

  private async _checkActiveAlerts(): Promise<void> {
    // Check and update active alerts
  }

  private async _persistMetricsData(): Promise<void> {
    // Persist metrics data to storage
  }

  private async _updateMetricsConfiguration(configuration: TMetricsConfiguration): Promise<void> {
    // Update metrics configuration
  }

  // Validation helper methods
  private async _validateMetricsCollectorHealth(
    errors: TValidationError[],
    warnings: TValidationWarning[]
  ): Promise<void> {
    // Check error rate
    if (this._collectorMetrics.errorCount > 100) {
      warnings.push({
        code: VALIDATION_WARNING_CODES.GOVERNANCE_RECOMMENDATION,
        message: `High error count: ${this._collectorMetrics.errorCount}`,
        severity: 'warning',
        timestamp: new Date(),
        component: this._componentType
      });
    }
  }

  private async _validateMetricDefinitions(
    errors: TValidationError[],
    warnings: TValidationWarning[]
  ): Promise<void> {
    // Check if basic metrics are defined
    if (this._metricDefinitions.size === 0) {
      errors.push({
        code: VALIDATION_ERROR_CODES.GOVERNANCE_VIOLATION,
        message: 'No metric definitions found',
        severity: 'error',
        timestamp: new Date(),
        component: this._componentType
      });
    }
  }

  private async _validateDataIntegrity(
    errors: TValidationError[],
    warnings: TValidationWarning[]
  ): Promise<void> {
    // Check data integrity
    let totalDataPoints = 0;
    for (const metrics of Array.from(this._realTimeMetrics.values())) {
      totalDataPoints += metrics.length;
    }

    if (totalDataPoints === 0 && this._collectorMetrics.totalDataPointsCollected > 0) {
      warnings.push({
        code: VALIDATION_WARNING_CODES.GOVERNANCE_RECOMMENDATION,
        message: 'No data points in storage despite collection activity',
        severity: 'warning',
        timestamp: new Date(),
        component: this._componentType
      });
    }
  }

  /**
   * Calculate performance score based on metrics
   */
  private _calculatePerformanceScore(
    avgExecutionTime: number,
    avgMemoryUsage: number,
    avgCpuUsage: number
  ): number {
    // Base score of 100, subtract penalties
    let score = 100;

    // Execution time penalties
    if (avgExecutionTime > 5000) { // > 5 seconds
      score -= 30;
    } else if (avgExecutionTime > 1000) { // > 1 second
      score -= 15;
    } else if (avgExecutionTime > 500) { // > 500ms
      score -= 5;
    }

    // Memory usage penalties (assuming MB)
    if (avgMemoryUsage > 1000) { // > 1GB
      score -= 25;
    } else if (avgMemoryUsage > 500) { // > 500MB
      score -= 10;
    } else if (avgMemoryUsage > 100) { // > 100MB
      score -= 5;
    }

    // CPU usage penalties (percentage)
    if (avgCpuUsage > 80) { // > 80%
      score -= 20;
    } else if (avgCpuUsage > 60) { // > 60%
      score -= 10;
    } else if (avgCpuUsage > 40) { // > 40%
      score -= 5;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Generate performance recommendations
   */
  private _generatePerformanceRecommendations(
    avgExecutionTime: number,
    avgMemoryUsage: number,
    avgCpuUsage: number
  ): string[] {
    const recommendations: string[] = [];

    if (avgExecutionTime > 1000) {
      recommendations.push('Consider optimizing rule logic to reduce execution time');
      recommendations.push('Review rule complexity and consider breaking down complex rules');
    }

    if (avgMemoryUsage > 500) {
      recommendations.push('Monitor memory usage patterns and implement memory optimization');
      recommendations.push('Consider implementing rule result caching to reduce memory overhead');
    }

    if (avgCpuUsage > 60) {
      recommendations.push('CPU usage is high, consider load balancing or rule optimization');
      recommendations.push('Review rule execution patterns for optimization opportunities');
    }

    if (recommendations.length === 0) {
      recommendations.push('Performance metrics are within acceptable ranges');
    }

    return recommendations;
  }

  /**
   * Get system metric summary
   */
  private async _getSystemMetricSummary(
    metricId: string,
    startTime: Date,
    endTime: Date
  ): Promise<{
    totalCount: number;
    averageValue: number;
    minValue: number;
    maxValue: number;
    latestValue: number;
  }> {
    const rawData = await this.getMetricData(metricId, startTime, endTime) as IMetricDataPoint[];

    if (rawData.length === 0) {
      return {
        totalCount: 0,
        averageValue: 0,
        minValue: 0,
        maxValue: 0,
        latestValue: 0
      };
    }

    const values = rawData.map(d => d.value);
    const sum = values.reduce((a, b) => a + b, 0);

    return {
      totalCount: rawData.length,
      averageValue: sum / rawData.length,
      minValue: Math.min(...values),
      maxValue: Math.max(...values),
      latestValue: rawData[rawData.length - 1].value
    };
  }

  /**
   * Calculate overall system score
   */
  private _calculateSystemScore(allMetrics: any): number {
    let score = 100;

    // Factor in rule execution performance
    if (allMetrics.ruleExecutions.averageValue > 1000) {
      score -= 15;
    }

    // Factor in compliance violations
    if (allMetrics.complianceViolations.totalCount > 10) {
      score -= 20;
    }

    // Factor in system health
    if (allMetrics.systemHealth.errorCount > 50) {
      score -= 25;
    }

    // Factor in active alerts
    if (allMetrics.systemHealth.activeAlerts > 5) {
      score -= 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Generate system recommendations
   */
  private _generateSystemRecommendations(allMetrics: any): string[] {
    const recommendations: string[] = [];

    if (allMetrics.ruleExecutions.averageValue > 1000) {
      recommendations.push('Rule execution times are high, consider performance optimization');
    }

    if (allMetrics.complianceViolations.totalCount > 10) {
      recommendations.push('High number of compliance violations detected, review governance policies');
    }

    if (allMetrics.systemHealth.errorCount > 50) {
      recommendations.push('System error count is elevated, investigate error patterns');
    }

    if (allMetrics.systemHealth.activeAlerts > 5) {
      recommendations.push('Multiple active alerts, review and resolve alert conditions');
    }

    if (recommendations.length === 0) {
      recommendations.push('System metrics are within acceptable ranges');
    }

    return recommendations;
  }

  /**
   * Convert data to CSV format
   */
  private _convertToCSV(exportData: any): string {
    const lines: string[] = [];
    
    // Add header
    lines.push('MetricID,Timestamp,Value,Tags,Source');
    
    // Add data points
    for (const [metricId, metricData] of Object.entries(exportData.metrics)) {
      const data = metricData as any;
      for (const dataPoint of data.rawData) {
        const tags = Object.entries(dataPoint.tags).map(([k, v]) => `${k}:${v}`).join(';');
        lines.push(`${metricId},${dataPoint.timestamp},${dataPoint.value},${tags},${dataPoint.source}`);
      }
    }
    
    return lines.join('\n');
  }

  /**
   * Convert data to Excel format (placeholder)
   */
  private _convertToExcel(exportData: any): any {
    // This would require a proper Excel library implementation
    // For now, return JSON representation
    return {
      format: 'excel',
      note: 'Excel export requires additional library implementation',
      data: exportData
    };
  }
} 